<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vinay <PERSON>re - Backend Developer</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <div class="header-content">
                <div class="name-title">
                    <h1>Vinay Lokre</h1>
                    <h2>Backend Developer</h2>
                </div>
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+91 ************</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Bangalore, India</span>
                    </div>
                    <div class="contact-item">
                        <i class="fab fa-linkedin"></i>
                        <span>linkedin.com/in/vinay-lokre</span>
                    </div>
                </div>
            </div>
            <button class="print-btn" onclick="window.print()">
                <i class="fas fa-print"></i> Print Resume
            </button>
        </header>

        <!-- Profile Summary -->
        <section class="section">
            <h3 class="section-title">Profile Summary</h3>
            <p class="summary-text">
                Results-oriented <strong>Backend Developer</strong> with expertise in <strong>C#, .NET, and Blazor</strong>, specializing in scalable backend systems and CRM applications. Proven ability to deliver robust microservices, RESTful APIs, and enterprise-grade business logic. Skilled in cloud platforms (Azure, AWS, Google Cloud), containerization (Docker, Kubernetes), and PostgreSQL. Currently leading 3 backend engineers at <strong>Leadrat (Dhinwa Solutions)</strong>.
            </p>
        </section>

        <!-- Main Content Grid -->
        <div class="main-content">
            <!-- Left Column -->
            <div class="left-column">
                <!-- Professional Experience -->
                <section class="section">
                    <h3 class="section-title">Professional Experience</h3>
                    <div class="experience-item">
                        <div class="job-header">
                            <h4>Backend Developer</h4>
                            <span class="company">Leadrat (Dhinwa Solutions)</span>
                            <span class="duration">2023 – Present</span>
                        </div>
                        <ul class="job-responsibilities">
                            <li>Leading team of 3 backend developers on .NET-based CRM architecture</li>
                            <li>Built CRM systems using <strong>C#, .NET 6+, and Blazor</strong></li>
                            <li>Designed scalable REST APIs and backend workflows</li>
                            <li>Implemented Docker and Kubernetes containerized deployment</li>
                            <li>Developed backend integrations with WhatsApp, IVR, and social APIs</li>
                            <li>Led PostgreSQL data migration and schema optimization</li>
                        </ul>
                    </div>
                </section>

                <!-- Key Projects -->
                <section class="section">
                    <h3 class="section-title">Key Projects</h3>
                    <div class="projects-list">
                        <div class="project-item">
                            <h4>CRM Backend & Admin Panel</h4>
                            <p>Built using <strong>C#/.NET</strong> and Blazor for customer relationship management</p>
                        </div>
                        <div class="project-item">
                            <h4>WhatsApp CRM Integration</h4>
                            <p>Lead and message handling via APIs with real-time communication</p>
                        </div>
                        <div class="project-item">
                            <h4>PostgreSQL Migration & DB Splitting</h4>
                            <p>Optimized for reporting and analytics with improved performance</p>
                        </div>
                        <div class="project-item">
                            <h4>Call Transcription Tool</h4>
                            <p>Voice-to-text using Python + Speech AI integration</p>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Right Column -->
            <div class="right-column">
                <!-- Skills -->
                <section class="section">
                    <h3 class="section-title">Technical Skills</h3>
                    <div class="skills-list">
                        <div class="skill-category">
                            <h4>Languages & Frameworks</h4>
                            <span class="skills">C#, .NET, Blazor, Java, Python</span>
                        </div>
                        <div class="skill-category">
                            <h4>Technologies</h4>
                            <span class="skills">REST APIs, Web Services, JSON, Docker, Kubernetes</span>
                        </div>
                        <div class="skill-category">
                            <h4>Databases</h4>
                            <span class="skills">PostgreSQL, NoSQL</span>
                        </div>
                        <div class="skill-category">
                            <h4>Cloud Platforms</h4>
                            <span class="skills">Azure, AWS, Google Cloud</span>
                        </div>
                        <div class="skill-category">
                            <h4>Tools & Others</h4>
                            <span class="skills">Git, VS Code, Team Leadership, AI Agent Prompting, Microservices, DevOps</span>
                        </div>
                    </div>
                </section>

                <!-- Education -->
                <section class="section">
                    <h3 class="section-title">Education</h3>
                    <div class="education-list">
                        <div class="education-item">
                            <h4>B.E., Computer Science</h4>
                            <span class="institution">Vemana IT</span>
                            <span class="year">2018 – 2022</span>
                        </div>
                        <div class="education-item">
                            <h4>PGP in Computational Data Science</h4>
                            <span class="institution">INSOFE</span>
                            <span class="year">2022 – 2023</span>
                        </div>
                    </div>
                </section>

            </div>
        </div>

        <!-- Achievements - Full Width Bottom Section -->
        <section class="section achievements-bottom">
            <h3 class="section-title">Achievements</h3>
            <div class="achievements-grid">
                <div class="achievement-item">
                    <span class="achievement-text">Managing 3-member .NET backend team</span>
                </div>
                <div class="achievement-item">
                    <span class="achievement-text"><strong>Multiple-time corporate hackathon winner</strong></span>
                </div>
                <div class="achievement-item">
                    <span class="achievement-text">Recognized for <strong>.NET technical mentoring and backend expertise</strong></span>
                </div>
                <div class="achievement-item">
                    <span class="achievement-text">Building scalable CRM architectures with <strong>.NET Core, Docker, and Azure</strong></span>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
