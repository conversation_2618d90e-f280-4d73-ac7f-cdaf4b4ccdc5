/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8fafc;
}

.container {
    max-width: 210mm; /* A4 width */
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    min-height: 297mm; /* A4 height */
    padding: 20mm;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 3px solid #2563eb;
    position: relative;
}

.header-content {
    flex: 1;
}

.name-title h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e40af;
    margin-bottom: 5px;
}

.name-title h2 {
    font-size: 1.3rem;
    font-weight: 500;
    color: #64748b;
    margin-bottom: 15px;
}

.contact-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.contact-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #475569;
}

.contact-item i {
    color: #2563eb;
    width: 16px;
    margin-right: 8px;
}

.print-btn {
    background: #2563eb;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.print-btn:hover {
    background: #1d4ed8;
}

.print-btn i {
    font-size: 0.8rem;
}

/* Section Styles */
.section {
    margin-bottom: 25px;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 12px;
    padding-bottom: 5px;
    border-bottom: 2px solid #e2e8f0;
}

.summary-text {
    font-size: 0.95rem;
    line-height: 1.7;
    color: #475569;
    text-align: justify;
}

/* Skills Styles */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.skill-category h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 5px;
}

.skills {
    font-size: 0.85rem;
    color: #64748b;
    line-height: 1.5;
}

/* Experience Styles */
.experience-item {
    margin-bottom: 20px;
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.job-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e40af;
}

.company {
    font-size: 0.95rem;
    color: #64748b;
    font-weight: 500;
}

.duration {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
}

.job-responsibilities {
    list-style: none;
    padding-left: 0;
}

.job-responsibilities li {
    font-size: 0.9rem;
    color: #475569;
    margin-bottom: 6px;
    padding-left: 15px;
    position: relative;
}

.job-responsibilities li::before {
    content: "•";
    color: #2563eb;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Projects Styles */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.project-item {
    background: #f8fafc;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #2563eb;
}

.project-item h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 6px;
}

.project-item p {
    font-size: 0.85rem;
    color: #64748b;
    line-height: 1.5;
}

/* Education Styles */
.education-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.education-item h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 4px;
}

.institution {
    font-size: 0.9rem;
    color: #64748b;
    display: block;
    margin-bottom: 2px;
}

.year {
    font-size: 0.85rem;
    color: #94a3b8;
}

/* Achievements Styles */
.achievements-list {
    list-style: none;
    padding-left: 0;
}

.achievements-list li {
    font-size: 0.9rem;
    color: #475569;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.achievements-list li::before {
    content: "★";
    color: #2563eb;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Print Styles */
@media print {
    body {
        background: white;
    }
    
    .container {
        box-shadow: none;
        margin: 0;
        padding: 15mm;
        max-width: none;
    }
    
    .print-btn {
        display: none;
    }
    
    .section {
        margin-bottom: 20px;
    }
    
    .header {
        margin-bottom: 25px;
    }
    
    /* Ensure content fits on one page */
    .section-title {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }
    
    .summary-text,
    .job-responsibilities li,
    .achievements-list li {
        font-size: 0.85rem;
    }
    
    .skills,
    .project-item p {
        font-size: 0.8rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .contact-info {
        grid-template-columns: 1fr;
    }
    
    .skills-grid,
    .projects-grid,
    .education-grid {
        grid-template-columns: 1fr;
    }
    
    .job-header {
        flex-direction: column;
        align-items: flex-start;
    }
}
