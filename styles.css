/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', Arial, sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 210mm; /* A4 width */
    margin: 0 auto;
    background: white;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    min-height: 297mm; /* A4 height */
    padding: 20mm;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    position: relative;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.header-content {
    flex: 1;
}

.name-title h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.name-title h2 {
    font-size: 1.3rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 15px;
}

.contact-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.contact-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.95);
}

.contact-item i {
    color: #ffd700;
    width: 16px;
    margin-right: 8px;
}

.print-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.print-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.print-btn i {
    font-size: 0.8rem;
}

/* Section Styles */
.section {
    margin-bottom: 25px;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 3px solid transparent;
    background: linear-gradient(90deg, #667eea, #764ba2);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
}

.summary-text {
    font-size: 0.95rem;
    line-height: 1.7;
    color: #475569;
    text-align: justify;
}

/* Skills Styles */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.skill-category {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    transition: transform 0.2s ease;
}

.skill-category:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.skill-category h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 5px;
}

.skills {
    font-size: 0.85rem;
    color: #64748b;
    line-height: 1.5;
}

/* Experience Styles */
.experience-item {
    margin-bottom: 20px;
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.job-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #4c51bf;
}

.company {
    font-size: 0.95rem;
    color: #64748b;
    font-weight: 500;
}

.duration {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
}

.job-responsibilities {
    list-style: none;
    padding-left: 0;
}

.job-responsibilities li {
    font-size: 0.9rem;
    color: #475569;
    margin-bottom: 6px;
    padding-left: 15px;
    position: relative;
}

.job-responsibilities li::before {
    content: "▶";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Projects Styles */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.project-item {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 18px;
    border-radius: 10px;
    border-left: 4px solid #764ba2;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.project-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(118, 75, 162, 0.15);
}

.project-item h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 6px;
}

.project-item p {
    font-size: 0.85rem;
    color: #64748b;
    line-height: 1.5;
}

/* Education Styles */
.education-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.education-item {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.education-item h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 4px;
}

.institution {
    font-size: 0.9rem;
    color: #64748b;
    display: block;
    margin-bottom: 2px;
}

.year {
    font-size: 0.85rem;
    color: #94a3b8;
}

/* Achievements Styles */
.achievements-list {
    list-style: none;
    padding-left: 0;
}

.achievements-list li {
    font-size: 0.9rem;
    color: #475569;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.achievements-list li::before {
    content: "🏆";
    font-size: 1.1em;
    position: absolute;
    left: 0;
}

/* Print Styles */
@media print {
    body {
        background: white;
    }

    .container {
        box-shadow: none;
        margin: 0;
        padding: 15mm;
        max-width: none;
        border-radius: 0;
    }

    .header {
        background: #4c51bf !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .print-btn {
        display: none;
    }
    
    .section {
        margin-bottom: 20px;
    }
    
    .header {
        margin-bottom: 25px;
    }
    
    /* Ensure content fits on one page */
    .section-title {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }
    
    .summary-text,
    .job-responsibilities li,
    .achievements-list li {
        font-size: 0.85rem;
    }
    
    .skills,
    .project-item p {
        font-size: 0.8rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .contact-info {
        grid-template-columns: 1fr;
    }
    
    .skills-grid,
    .projects-grid,
    .education-grid {
        grid-template-columns: 1fr;
    }
    
    .job-header {
        flex-direction: column;
        align-items: flex-start;
    }
}
