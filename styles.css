/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', Arial, sans-serif;
    line-height: 1.5;
    color: #2d3748;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-size: 15px;
}

.container {
    max-width: 210mm; /* A4 width */
    margin: 0 auto;
    background: white;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    min-height: 297mm; /* A4 height */
    padding: 18mm;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    color: white;
    position: relative;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.name-title {
    flex: 0 0 auto;
}

.name-title h1 {
    font-size: 2.2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.name-title h2 {
    font-size: 1.2rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
    text-align: right;
    flex: 0 0 auto;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.95);
}

.contact-item i {
    color: #ffd700;
    width: 16px;
    margin-right: 8px;
    order: 2;
    margin-left: 8px;
    margin-right: 0;
}

.contact-item span,
.contact-item a {
    order: 1;
}

.linkedin-link {
    color: rgba(255, 255, 255, 0.95);
    text-decoration: none;
    transition: color 0.3s ease;
}

.linkedin-link:hover {
    color: #ffd700;
    text-decoration: underline;
}

.print-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: absolute;
    top: 20px;
    right: 20px;
}

.print-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.print-btn i {
    font-size: 0.8rem;
}

/* Section Styles */
.section {
    margin-bottom: 20px;
}

.section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 10px;
    padding-bottom: 6px;
    border-bottom: 3px solid transparent;
    background: linear-gradient(90deg, #667eea, #764ba2);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 45px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
}

.summary-text {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #475569;
    text-align: justify;
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 25px;
    align-items: start;
}

.left-column {
    min-height: 100%;
}

.right-column {
    min-height: 100%;
}

/* Skills Styles */
.skills-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.skill-category {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    transition: transform 0.2s ease;
}

.skill-category:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.skill-category h4 {
    font-size: 0.85rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 4px;
}

.skills {
    font-size: 0.8rem;
    color: #64748b;
    line-height: 1.4;
}

/* Experience Styles */
.experience-item {
    margin-bottom: 16px;
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.job-header h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #4c51bf;
}

.company {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
}

.duration {
    font-size: 0.85rem;
    color: #64748b;
    font-weight: 500;
}

.job-responsibilities {
    list-style: none;
    padding-left: 0;
}

.job-responsibilities li {
    font-size: 0.85rem;
    color: #475569;
    margin-bottom: 5px;
    padding-left: 15px;
    position: relative;
    line-height: 1.4;
}

.job-responsibilities li::before {
    content: "▶";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Projects Styles */
.projects-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.project-item {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #764ba2;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.project-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(118, 75, 162, 0.15);
}

.project-item h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 5px;
}

.project-item p {
    font-size: 0.8rem;
    color: #64748b;
    line-height: 1.4;
}

/* Education Styles */
.education-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.education-item {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.education-item h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 4px;
}

.institution {
    font-size: 0.8rem;
    color: #64748b;
    display: block;
    margin-bottom: 2px;
}

.year {
    font-size: 0.75rem;
    color: #94a3b8;
}

/* Achievements Styles */
.achievements-list {
    list-style: none;
    padding-left: 0;
}

.achievements-list li {
    font-size: 0.85rem;
    color: #475569;
    margin-bottom: 6px;
    padding-left: 18px;
    position: relative;
    line-height: 1.4;
}

.achievements-list li::before {
    content: "🏆";
    font-size: 0.9em;
    position: absolute;
    left: 0;
}

/* Bottom Achievements Section */
.achievements-bottom {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 2px solid #e2e8f0;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.achievement-item {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 12px 15px;
    border-radius: 8px;
    border-left: 4px solid #ffd700;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.achievement-item::before {
    content: '🏆';
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.1em;
    opacity: 0.7;
}

.achievement-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(255, 215, 0, 0.2);
    border-left-color: #ffb700;
}

.achievement-text {
    font-size: 0.85rem;
    color: #475569;
    line-height: 1.4;
    padding-left: 25px;
    display: block;
}

/* Print Styles */
@media print {
    body {
        background: white !important;
        font-size: 11px !important;
        line-height: 1.2 !important;
    }

    .container {
        box-shadow: none !important;
        margin: 0 !important;
        padding: 12mm !important;
        max-width: none !important;
        border-radius: 0 !important;
        min-height: auto !important;
    }

    .header {
        background: #4c51bf !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        margin-bottom: 10px !important;
        padding: 12px !important;
        page-break-inside: avoid !important;
    }

    .name-title h1 {
        font-size: 1.5rem !important;
        margin-bottom: 2px !important;
    }

    .name-title h2 {
        font-size: 1rem !important;
        margin-bottom: 6px !important;
    }

    .contact-item {
        font-size: 0.7rem !important;
    }

    .print-btn {
        display: none !important;
    }

    .main-content {
        grid-template-columns: 1.3fr 0.7fr !important;
        gap: 18px !important;
    }

    .section {
        margin-bottom: 12px !important;
        page-break-inside: avoid !important;
    }

    .section-title {
        font-size: 0.9rem !important;
        margin-bottom: 6px !important;
        page-break-after: avoid !important;
    }

    .summary-text {
        font-size: 0.75rem !important;
        line-height: 1.3 !important;
    }

    .skills-list,
    .projects-list,
    .education-list {
        gap: 8px !important;
    }

    .skill-category {
        padding: 8px !important;
    }

    .skill-category h4 {
        font-size: 0.7rem !important;
        margin-bottom: 2px !important;
    }

    .skills {
        font-size: 0.65rem !important;
        line-height: 1.2 !important;
    }

    .experience-item {
        margin-bottom: 10px !important;
    }

    .job-header {
        margin-bottom: 6px !important;
    }

    .job-header h4 {
        font-size: 0.8rem !important;
    }

    .company {
        font-size: 0.7rem !important;
    }

    .duration {
        font-size: 0.65rem !important;
    }

    .job-responsibilities li {
        font-size: 0.7rem !important;
        margin-bottom: 2px !important;
        line-height: 1.2 !important;
        padding-left: 12px !important;
    }

    .project-item {
        padding: 8px !important;
    }

    .project-item h4 {
        font-size: 0.75rem !important;
        margin-bottom: 2px !important;
    }

    .project-item p {
        font-size: 0.65rem !important;
        line-height: 1.2 !important;
    }

    .education-item {
        padding: 8px !important;
    }

    .education-item h4 {
        font-size: 0.75rem !important;
        margin-bottom: 2px !important;
    }

    .institution {
        font-size: 0.65rem !important;
        margin-bottom: 1px !important;
    }

    .year {
        font-size: 0.6rem !important;
    }

    .achievements-list li {
        font-size: 0.7rem !important;
        margin-bottom: 3px !important;
        line-height: 1.2 !important;
        padding-left: 15px !important;
    }

    .achievements-bottom {
        margin-top: 15px !important;
        padding-top: 10px !important;
        page-break-inside: avoid !important;
    }

    .achievements-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 8px !important;
    }

    .achievement-item {
        padding: 6px 8px !important;
    }

    .achievement-text {
        font-size: 0.65rem !important;
        line-height: 1.2 !important;
        padding-left: 18px !important;
    }

    .achievement-item::before {
        font-size: 0.8em !important;
        left: 4px !important;
    }

    /* Responsive adjustments for print */
    @page {
        margin: 8mm !important;
        size: A4 !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }

    .header {
        flex-direction: column;
        gap: 15px;
        position: relative;
    }

    .print-btn {
        position: static;
        align-self: center;
    }

    .contact-info {
        text-align: left;
    }

    .contact-item {
        justify-content: flex-start;
    }

    .contact-item i {
        order: 1;
        margin-right: 8px;
        margin-left: 0;
    }

    .contact-item span,
    .contact-item a {
        order: 2;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .job-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .achievements-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}
