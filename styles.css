/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', Arial, sans-serif;
    line-height: 1.4;
    color: #2d3748;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-size: 14px;
}

.container {
    max-width: 210mm; /* A4 width */
    margin: 0 auto;
    background: white;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    min-height: 297mm; /* A4 height */
    padding: 15mm;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding: 18px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    color: white;
    position: relative;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.header-content {
    flex: 1;
}

.name-title h1 {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 3px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.name-title h2 {
    font-size: 1.1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 12px;
}

.contact-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
}

.contact-item {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.95);
}

.contact-item i {
    color: #ffd700;
    width: 14px;
    margin-right: 6px;
}

.print-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.print-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.print-btn i {
    font-size: 0.8rem;
}

/* Section Styles */
.section {
    margin-bottom: 16px;
}

.section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 3px solid transparent;
    background: linear-gradient(90deg, #667eea, #764ba2);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
}

.summary-text {
    font-size: 0.8rem;
    line-height: 1.4;
    color: #475569;
    text-align: justify;
}

/* Skills Styles */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.skill-category {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 10px;
    border-radius: 6px;
    border-left: 3px solid #667eea;
    transition: transform 0.2s ease;
}

.skill-category:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(102, 126, 234, 0.15);
}

.skill-category h4 {
    font-size: 0.75rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 3px;
}

.skills {
    font-size: 0.7rem;
    color: #64748b;
    line-height: 1.3;
}

/* Experience Styles */
.experience-item {
    margin-bottom: 12px;
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    flex-wrap: wrap;
    gap: 8px;
}

.job-header h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #4c51bf;
}

.company {
    font-size: 0.8rem;
    color: #64748b;
    font-weight: 500;
}

.duration {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
}

.job-responsibilities {
    list-style: none;
    padding-left: 0;
}

.job-responsibilities li {
    font-size: 0.75rem;
    color: #475569;
    margin-bottom: 3px;
    padding-left: 12px;
    position: relative;
    line-height: 1.3;
}

.job-responsibilities li::before {
    content: "▶";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Projects Styles */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.project-item {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 10px;
    border-radius: 6px;
    border-left: 3px solid #764ba2;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.project-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.project-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(118, 75, 162, 0.15);
}

.project-item h4 {
    font-size: 0.8rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 3px;
}

.project-item p {
    font-size: 0.7rem;
    color: #64748b;
    line-height: 1.3;
}

/* Education Styles */
.education-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.education-item {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 10px;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

.education-item h4 {
    font-size: 0.8rem;
    font-weight: 600;
    color: #4c51bf;
    margin-bottom: 2px;
}

.institution {
    font-size: 0.75rem;
    color: #64748b;
    display: block;
    margin-bottom: 1px;
}

.year {
    font-size: 0.7rem;
    color: #94a3b8;
}

/* Achievements Styles */
.achievements-list {
    list-style: none;
    padding-left: 0;
}

.achievements-list li {
    font-size: 0.75rem;
    color: #475569;
    margin-bottom: 4px;
    padding-left: 16px;
    position: relative;
    line-height: 1.3;
}

.achievements-list li::before {
    content: "🏆";
    font-size: 0.9em;
    position: absolute;
    left: 0;
}

/* Print Styles */
@media print {
    * {
        margin: 0 !important;
        padding: 0 !important;
    }

    body {
        background: white !important;
        font-size: 10px !important;
        line-height: 1.1 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .container {
        box-shadow: none !important;
        margin: 0 !important;
        padding: 8mm !important;
        max-width: none !important;
        border-radius: 0 !important;
        min-height: auto !important;
        height: auto !important;
        max-height: 277mm !important;
        overflow: hidden !important;
    }

    .header {
        background: #4c51bf !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        margin-bottom: 6px !important;
        padding: 8px !important;
        page-break-inside: avoid !important;
    }

    .name-title h1 {
        font-size: 1.3rem !important;
        margin-bottom: 1px !important;
        line-height: 1 !important;
    }

    .name-title h2 {
        font-size: 0.9rem !important;
        margin-bottom: 4px !important;
        line-height: 1 !important;
    }

    .contact-info {
        gap: 2px !important;
    }

    .contact-item {
        font-size: 0.6rem !important;
        line-height: 1 !important;
    }

    .contact-item i {
        width: 10px !important;
        margin-right: 3px !important;
    }

    .print-btn {
        display: none !important;
    }

    .section {
        margin-bottom: 6px !important;
        page-break-inside: avoid !important;
    }

    .section-title {
        font-size: 0.8rem !important;
        margin-bottom: 3px !important;
        page-break-after: avoid !important;
        line-height: 1 !important;
    }

    .section-title::after {
        width: 25px !important;
        height: 1px !important;
    }

    .summary-text {
        font-size: 0.6rem !important;
        line-height: 1.1 !important;
        margin-bottom: 0 !important;
    }

    .skills-grid,
    .projects-grid,
    .education-grid {
        gap: 4px !important;
    }

    .skill-category {
        padding: 4px !important;
        margin-bottom: 2px !important;
    }

    .skill-category h4 {
        font-size: 0.55rem !important;
        margin-bottom: 1px !important;
        line-height: 1 !important;
    }

    .skills {
        font-size: 0.5rem !important;
        line-height: 1.1 !important;
    }

    .experience-item {
        margin-bottom: 6px !important;
    }

    .job-header {
        margin-bottom: 3px !important;
        gap: 4px !important;
    }

    .job-header h4 {
        font-size: 0.7rem !important;
        line-height: 1 !important;
    }

    .company {
        font-size: 0.6rem !important;
        line-height: 1 !important;
    }

    .duration {
        font-size: 0.55rem !important;
        line-height: 1 !important;
    }

    .job-responsibilities li {
        font-size: 0.55rem !important;
        margin-bottom: 1px !important;
        line-height: 1.1 !important;
        padding-left: 8px !important;
    }

    .project-item {
        padding: 4px !important;
        margin-bottom: 2px !important;
    }

    .project-item h4 {
        font-size: 0.6rem !important;
        margin-bottom: 1px !important;
        line-height: 1 !important;
    }

    .project-item p {
        font-size: 0.5rem !important;
        line-height: 1.1 !important;
    }

    .education-item {
        padding: 4px !important;
    }

    .education-item h4 {
        font-size: 0.6rem !important;
        margin-bottom: 0px !important;
        line-height: 1 !important;
    }

    .institution {
        font-size: 0.55rem !important;
        margin-bottom: 0px !important;
        line-height: 1 !important;
    }

    .year {
        font-size: 0.5rem !important;
        line-height: 1 !important;
    }

    .achievements-list li {
        font-size: 0.55rem !important;
        margin-bottom: 1px !important;
        line-height: 1.1 !important;
        padding-left: 12px !important;
    }

    .achievements-list li::before {
        font-size: 0.7em !important;
    }

    /* Force single page - aggressive */
    @page {
        margin: 0 !important;
        size: A4 !important;
    }

    html, body {
        height: auto !important;
        overflow: visible !important;
        max-height: 297mm !important;
    }

    .container {
        transform: scale(0.95) !important;
        transform-origin: top left !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
    }
    
    .contact-info {
        grid-template-columns: 1fr;
    }
    
    .skills-grid,
    .projects-grid,
    .education-grid {
        grid-template-columns: 1fr;
    }
    
    .job-header {
        flex-direction: column;
        align-items: flex-start;
    }
}
